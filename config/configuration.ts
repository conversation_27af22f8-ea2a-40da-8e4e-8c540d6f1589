const dotenv = require('dotenv');
dotenv.config();
export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  database: {
    type: 'postgres',
    host: process.env.DATABASE_HOST_M,
    port: parseInt(process.env.DATABASE_PORT_M),
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    schema: process.env.DATABASE_SCHEMA,
    entities: ['dist/**/*.entity{.ts,.js}'],
    migrations: ['src/migration/**/*.ts'],
    synchronize: process.env.SYNCHRONIZE,
    charset: 'utf8mb4',
    seeder_sync: process.env.SEEDER_SYNC,
  },
  jwtConstant: {
    secret: process.env.SECRET,
    admin_jwt_expires_in: process.env.ADMIN_JWT_EXPIRES_IN,
    user_jwt_expires_in: process.env.USER_JWT_EXPIRES_IN,
  },
  moralis: {
    moralis_key: process.env.MORALIS_KEY,
    moralis_secret: process.env.MORALIS_SECRET,
  },
  redis: process.env.REDIS,

  chain: process.env.CHAIN,
  chain_id: parseInt(process.env.CHAINID),

  rpc_endpoint: process.env.RPC_ENDPOINT,
  qn_token: process.env.QN_TOKEN,
  qn_api_key: process.env.QN_API_KEY,

  qn_endpoint: process.env.QN_ENDPOINT,
  qn_endpoint_token: process.env.QN_ENDPOINT_TOKEN,
});
