// seeders/network.seeder.ts
import { Injectable } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { Network, NetworkType } from 'src/networks/entities/network.entity';
import { EntityManager } from 'typeorm';

@Injectable()
export class NetworkSeeder {
  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager
  ) {}

  async seedNetworks(): Promise<void> {
    const networks = [
      // Ethereum Networks
      {
        display_name: "ethereum-mainnet",
        name: "Ethereum Mainnet",
        symbol: "ETH",
        chain_id: 1,
        type: NetworkType.EVM,
        block_time_seconds: 12,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "eth_blockNumber",
          params: [],
          id: 1
        },
        is_active: true
      },
      {
        display_name: "ethereum-sepolia",
        name: "Ethereum Sepolia",
        symbol: "ETH",
        chain_id: 11155111,
        type: NetworkType.EVM,
        block_time_seconds: 12,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "eth_blockNumber",
          params: [],
          id: 1
        },
        is_active: true
      },

      // BSC Networks
      {
        display_name: "bsc",
        name: "BSC Mainnet",
        symbol: "BNB",
        chain_id: 56,
        type: NetworkType.EVM,
        block_time_seconds: 3,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "eth_blockNumber",
          params: [],
          id: 1
        },
        is_active: true
      },
      {
        display_name: "bsc-sepolia",
        name: "BSC Testnet",
        symbol: "tBNB",
        chain_id: 97,
        type: NetworkType.EVM,
        block_time_seconds: 3,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "eth_blockNumber",
          params: [],
          id: 1
        },
        is_active: true
      },

      // Polygon Networks
      {
        display_name: "matic",
        name: "Polygon Mainnet",
        symbol: "MATIC",
        chain_id: 137,
        type: NetworkType.EVM,
        block_time_seconds: 2,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "eth_blockNumber",
          params: [],
          id: 1
        },
        is_active: true
      },
      {
        display_name: "matic-sepolia",
        name: "Polygon Sepolia",
        symbol: "MATIC",
        chain_id: 80001,
        type: NetworkType.EVM,
        block_time_seconds: 2,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "eth_blockNumber",
          params: [],
          id: 1
        },
        is_active: true
      },

      // Base Networks
      {
        display_name: "base-mainnet",
        name: "Base Mainnet",
        symbol: "ETH",
        chain_id: 8453,
        type: NetworkType.EVM,
        block_time_seconds: 2,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "eth_blockNumber",
          params: [],
          id: 1
        },
        is_active: true
      },
      {
        display_name: "base-sepolia",
        name: "Base Sepolia",
        symbol: "ETH",
        chain_id: 84532,
        type: NetworkType.EVM,
        block_time_seconds: 2,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "eth_blockNumber",
          params: [],
          id: 1
        },
        is_active: true
      },

      // Arbitrum Networks
      {
        display_name: "arbitrum-mainnet",
        name: "Arbitrum One",
        symbol: "ETH",
        chain_id: 42161,
        type: NetworkType.EVM,
        block_time_seconds: 1,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "eth_blockNumber",
          params: [],
          id: 1
        },
        is_active: true
      },
      {
        display_name: "arbitrum-sepolia",
        name: "Arbitrum Sepolia",
        symbol: "ETH",
        chain_id: 421614,
        type: NetworkType.EVM,
        block_time_seconds: 1,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "eth_blockNumber",
          params: [],
          id: 1
        },
        is_active: true
      },

      // Solana Networks
      {
        display_name: "solana-mainnet",
        name: "Solana Mainnet",
        symbol: "SOL",
        chain_id: null, // Solana doesn't use chain_id
        type: NetworkType.SOLANA,
        block_time_seconds: 1,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "getSlot",
          params: [],
          id: 1
        },
        is_active: true
      },
      {
        display_name: "solana-devnet",
        name: "Solana Devnet",
        symbol: "SOL",
        chain_id: null,
        type: NetworkType.SOLANA,
        block_time_seconds: 1,
        rpc_payload: {
          jsonrpc: "2.0",
          method: "getSlot",
          params: [],
          id: 1
        },
        is_active: true
      }
    ];

    for (const networkData of networks) {
      await this.seedSingleNetwork(networkData);
    }

    console.log('Network seeding completed');
  }

  private async seedSingleNetwork(networkData: any): Promise<void> {
    try {
      const existingNetwork = await this.entityManager
        .createQueryBuilder(Network, 'network')
        .where('network.display_name = :display_name', { display_name: networkData.display_name })
        .getOne();

      if (!existingNetwork) {
        await this.entityManager.save(Network, networkData);
        console.log(`Created network: ${networkData.name}`);
      } else {
        console.log(`⚠️ Network already exists: ${networkData.name}`);
      }
    } catch (error) {
      console.error(`❌ Failed to seed network ${networkData.name}:`, error.message);
    }
  }
}