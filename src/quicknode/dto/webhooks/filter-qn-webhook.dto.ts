import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsString } from "class-validator";

export enum NetworkType {
  ETHEREUM = 'ethereum-mainnet',
  POLYGON = 'polygon-mainnet', 
  ARBITRUM = 'arbitrum-mainnet',
  BITCOIN = 'bitcoin-mainnet',
}

export class FilterQnWebhookDto {

  @ApiProperty({example: "ethereum-mainnet"})
  @IsEnum(NetworkType)
  network: NetworkType;

  @ApiProperty({example: "17811625"})
  @IsString()
  block: string;

  @ApiProperty({example: "ZnVuY3Rpb24gbWFpbihwYXlsb2FkKSB7CiAgY29uc3QgewogICAgZGF0YSwKICAgIG1ldGFkYXRhLAogIH0gPSBwYXlsb2FkOwoKICAvLyBsb2dpYyB0byBmaWx0ZXIgZGF0YQoKICByZXR1cm4gcGF5bG9hZDsKfQ=="})
  @IsString()
  filter_function: string;
}
