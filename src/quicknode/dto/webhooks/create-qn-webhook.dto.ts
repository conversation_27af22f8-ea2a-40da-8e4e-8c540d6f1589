import { Optional } from "@nestjs/common";
import { ApiProperty } from "@nestjs/swagger";
import { IsEmail, IsEnum, IsString, IsUrl } from "class-validator";

export enum NetworkType {
  ETHEREUM = 'ethereum-mainnet',
  POLYGON = 'polygon-mainnet', 
  ARBITRUM = 'arbitrum-mainnet',
  BITCOIN = 'bitcoin-mainnet',
}

// can we do partial type 
export class CreateQnWebhookDto {
  @ApiProperty({ example: 'Name Here Now'})
  @IsString()
  name: string;

  @ApiProperty({ example: 'ethereum-mainnet'})
  @IsEnum(NetworkType)
  network: NetworkType;

  @ApiProperty({ example: '<EMAIL>'})
  @IsEmail()
  notification_email: string;

  @ApiProperty({ example: 'https://webhook.site/bff9438c-8c87-4b71-9a2c-dfe553fec52' })
  @IsUrl()
  destination_url: string;

  get destination_attributes() {
    return {
      url: this.destination_url,
      compression: 'none'
    };
  }

  @IsString()
  @ApiProperty({ example: '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'})
  @Optional()
  filter_function?: string;
}
