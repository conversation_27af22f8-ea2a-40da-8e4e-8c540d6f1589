import { IsString, IsEnum, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { NetworkEnum, DatasetEnum } from './create-qn-stream.dto';

export class FilterQnStreamDto {
  @ApiProperty({
    example: NetworkEnum.ETHEREUM_MAINNET,
  })
  @IsEnum(NetworkEnum)
  network: NetworkEnum;

  @ApiProperty({
    example: DatasetEnum.BLOCK,
  })
  @IsEnum(DatasetEnum)
  dataset: DatasetEnum;

  @ApiProperty({
    example: '69007', // or "0x10D4F" whoch is not accepted no hex 
  })
  @IsString()
  block: string;

  @ApiProperty({
    example:
      'ZnVuY3Rpb24gbWFpbihkYXRhKSB7IHJldHVybiBkYXRhLnZhbHVlID4gMTAwOyB9', // simple >100 example
  })
  @IsString()
  filter_function: string;
}
