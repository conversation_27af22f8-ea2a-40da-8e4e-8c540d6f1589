import { IsString, IsOptional, IsNumber, IsBoolean, IsEnum, IsObject } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { NetworkEnum, DatasetEnum, RegionEnum, StatusEnum, DestinationEnum } from './create-qn-stream.dto';

export class UpdateQnStreamDto {
  @ApiPropertyOptional({
    description: 'Stream name',
    example: 'Updated Stream Name'
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Blockchain network',
    enum: NetworkEnum,
    example: 'ethereum-mainnet'
  })
  @IsOptional()
  @IsEnum(NetworkEnum)
  network?: NetworkEnum;

  @ApiPropertyOptional({
    description: 'Dataset type to stream',
    enum: DatasetEnum,
    example: 'block'
  })
  @IsOptional()
  @IsEnum(DatasetEnum)
  dataset?: DatasetEnum;

  @ApiPropertyOptional({
    description: 'Base64 encoded JavaScript function for filtering',
    example: 'ZnVuY3Rpb24gdGVzdCh0eCkgeyByZXR1cm4gdHguZnJvbT09PSAiMHgxMjMiOyB9'
  })
  @IsOptional()
  @IsString()
  filter_function?: string;

  @ApiPropertyOptional({
    description: 'Region for stream processing',
    enum: RegionEnum,
    example: 'usa_east'
  })
  @IsOptional()
  @IsEnum(RegionEnum)
  region?: RegionEnum;

  @ApiPropertyOptional({
    description: 'Start block number',
    example: 100
  })
  @IsOptional()
  @IsNumber()
  start_range?: number;

  @ApiPropertyOptional({
    description: 'End block number',
    example: 200
  })
  @IsOptional()
  @IsNumber()
  end_range?: number;

  @ApiPropertyOptional({
    description: 'Number of dataset items to batch',
    example: 10
  })
  @IsOptional()
  @IsNumber()
  dataset_batch_size?: number;

  @ApiPropertyOptional({
    description: 'Stream metadata options',
    example: 'body',
    enum: ['body', 'headers', 'none']
  })
  @IsOptional()
  @IsString()
  include_stream_metadata?: string;

  @ApiPropertyOptional({
    description: 'Destination type for stream output',
    enum: DestinationEnum,
    example: 'webhook'
  })
  @IsOptional()
  @IsEnum(DestinationEnum)
  destination?: DestinationEnum;

  @ApiPropertyOptional({
    description: 'Fix block reorgs (0 = false, 1 = true)',
    example: 0
  })
  @IsOptional()
  @IsNumber()
  fix_block_reorgs?: number;

  @ApiPropertyOptional({
    description: 'Keep distance from blockchain tip',
    example: 0
  })
  @IsOptional()
  @IsNumber()
  keep_distance_from_tip?: number;

  @ApiPropertyOptional({
    description: 'Enable elastic batching',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  elastic_batch_enabled?: boolean;

  @ApiPropertyOptional({
    description: 'Destination-specific attributes',
    example: {
      url: 'https://webhook.site/updated-url',
      max_retry: 5,
      retry_interval_sec: 2,
      post_timeout_sec: 15
    }
  })
  @IsOptional()
  @IsObject()
  destination_attributes?: any;

  @ApiPropertyOptional({
    description: 'Stream status',
    enum: StatusEnum,
    example: 'active'
  })
  @IsOptional()
  @IsEnum(StatusEnum)
  status?: StatusEnum;

  @ApiPropertyOptional({
    description: 'Notification email for stream alerts',
    example: '<EMAIL>'
  })
  @IsOptional()
  @IsString()
  notification_email?: string;
}
