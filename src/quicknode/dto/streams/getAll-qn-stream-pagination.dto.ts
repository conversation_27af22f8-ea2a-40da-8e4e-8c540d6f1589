import { Transform, Type } from "class-transformer";
import { IsInt, IsOptional, <PERSON>, <PERSON> } from "class-validator";
import { ApiPropertyOptional } from "@nestjs/swagger";

export class GetAllQnStreamPaginationDto {
  @ApiPropertyOptional({
    description: 'Number of streams to return',
    minimum: 1,
    maximum: 100,
    example: 20,
    default: 20
  })
  @Min(1)
  @Max(100)
  @IsInt()
  @IsOptional()
  @Type(() => Number) 
  limit: number = 20; 

  @ApiPropertyOptional({
    description: 'Number of streams to skip',
    minimum: 0,
    example: 0,
    default: 0
  })
  @Min(0)
  @IsInt()
  @IsOptional()
  @Type(() => Number)
  offset: number = 0;
}
