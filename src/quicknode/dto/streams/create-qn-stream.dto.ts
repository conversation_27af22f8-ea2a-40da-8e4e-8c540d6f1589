import { IsString, IsOptional, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, IsEnum, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum NetworkEnum {
  ETHEREUM_MAINNET = 'ethereum-mainnet',
  ETHEREUM_SEPOLIA = 'ethereum-sepolia',
  POLYGON_MAINNET = 'polygon-mainnet',
  BSC_MAINNET = 'bsc-mainnet',
  AVALANCHE_MAINNET = 'avalanche-mainnet',
  OPTIMISM_MAINNET = 'optimism-mainnet',
  ARBITRUM_MAINNET = 'arbitrum-mainnet',
  BASE_MAINNET = 'base-mainnet',
  SOLANA_MAINNET = 'solana-mainnet',
  XRP_MAINNET = 'xrp-mainnet'
}

export enum DatasetEnum {
  BLOCK = 'block',
  BLOCK_WITH_RECEIPTS = 'block_with_receipts',
  RECEIPTS = 'receipts',
  TRANSACTIONS = 'transactions',
  LEDGER = 'ledger'
}

export enum RegionEnum {
  USA_EAST = 'usa_east',
  USA_WEST = 'usa_west',
  EUROPE = 'europe',
  ASIA_PACIFIC = 'asia_pacific'
}

export enum StatusEnum {
  ACTIVE = 'active',
  PAUSED = 'paused',
  TERMINATED = 'terminated'
}

export enum DestinationEnum {
  WEBHOOK = 'webhook',
  S3 = 's3',
  POSTGRES = 'postgres',
  FUNCTIONS = 'functions'
}

export class CreateQnStreamDto {
  @ApiProperty({ example: 'My Stream' })
  @IsString()
  name: string;

  @ApiProperty({ enum: NetworkEnum, example: NetworkEnum.ETHEREUM_MAINNET })
  @IsEnum(NetworkEnum)
  network: NetworkEnum;

  @ApiProperty({ enum: DatasetEnum, example: DatasetEnum.BLOCK })
  @IsEnum(DatasetEnum)
  dataset: DatasetEnum;

  // @ApiPropertyOptional({ 
  //   description: 'Base64 encoded JavaScript filter function',
  //   example: 'ZnVuY3Rpb24gbWFpbihkYXRhKSB7IHJldHVybiBkYXRhOyB9'
  // })
  // @IsOptional()
  // @IsString()
  // filter_function?: string;

  @ApiPropertyOptional({ enum: RegionEnum, example: RegionEnum.USA_EAST })
  @IsOptional()
  @IsEnum(RegionEnum)
  region?: RegionEnum;

  @ApiPropertyOptional({ example: 100 })
  @IsOptional()
  @IsNumber()
  start_range?: number;

  @ApiPropertyOptional({ example: 200 })
  @IsOptional()
  @IsNumber()
  end_range?: number;

  @ApiPropertyOptional({ example: 1 })
  @IsOptional()
  @IsNumber()
  dataset_batch_size?: number;

  @ApiPropertyOptional({ example: 'body', enum: ['body', 'headers', 'none'] })
  @IsOptional()
  @IsString()
  include_stream_metadata?: string;

  @ApiProperty({ enum: DestinationEnum, example: DestinationEnum.WEBHOOK })
  @IsEnum(DestinationEnum)
  destination: DestinationEnum;

  @ApiPropertyOptional({ example: 0 })
  @IsOptional()
  @IsNumber()
  fix_block_reorgs?: number;

  @ApiPropertyOptional({ example: 0 })
  @IsOptional()
  @IsNumber()
  keep_distance_from_tip?: number;

  @ApiPropertyOptional({ example: true })
  @IsOptional()
  @IsBoolean()
  elastic_batch_enabled?: boolean;

  @ApiProperty({
    example: {
      url: 'https://webhook.site/a71f249c-12f8-4e5c-9b17-b2525a5fde6e',
      compression: 'none',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-token'
      },
      max_retry: 3,
      retry_interval_sec: 1,
      post_timeout_sec: 10
    }
  })
  @IsObject()
  destination_attributes: {
    url: string;
    compression: string;
    headers?: Record<string, string>;
    max_retry?: number;
    retry_interval_sec?: number;
    post_timeout_sec?: number;
  };

  @ApiPropertyOptional({ enum: StatusEnum, example: StatusEnum.ACTIVE })
  @IsOptional()
  @IsEnum(StatusEnum)
  status?: StatusEnum;
}
