import { Controller, Get, Post, Body, Patch, Param, Delete, Query, DefaultValuePipe, ParseIntPipe } from '@nestjs/common';
import { QuicknodeService } from './quicknode.service';
import { CreateQnWebhookDto } from './dto/webhooks/create-qn-webhook.dto';
import { CreateQnWebhookTemplateDto } from './dto/webhooks/create-qn-webhook-template.dto';
import { UpdateQnWebhookDto } from './dto/webhooks/update-qn-webhook.dto';
import { FilterQnWebhookDto } from './dto/webhooks/filter-qn-webhook.dto';
import { CreateQnStreamDto } from './dto/streams/create-qn-stream.dto';
import { UpdateQnStreamDto } from './dto/streams/update-qn-stream.dto';
import { FilterQnStreamDto } from './dto/streams/filter-qn-streams.dto';

@Controller('quicknode')
export class QuicknodeController {
  constructor(private readonly quicknodeService: QuicknodeService) {}

  // ==================== D WEBHOOK METHODS ====================
  // create webhook 
  @Post('create/webhook')
  async createWebhook(@Body() dto: CreateQnWebhookDto) {
    console.log("Creating Quicknode webhook");
    return await this.quicknodeService.createWebhook(
      dto.name,
      dto.network, 
      dto.notification_email,
      dto.destination_url
    );
  }

  // create webhook with template ( filters, contract , wallet )
  @Post('create/webhook/template/evmWalletFilter')
  async createWebhookWithTemplate(@Body() dto: CreateQnWebhookTemplateDto) {
    console.log("Creating Quicknode webhook with template");
    const wallets = dto.templateArgs?.wallets || [];
    console.log("These are the existing wallets to filter\n", wallets);
    return await this.quicknodeService.createWebhookWithTemplate(
      dto.name,
      dto.network,
      dto.notification_email,
      dto.destination_url,
      dto.template,
      dto.filter_function,
      wallets
    );
  }

  // get all webhooks
  @Get("retrieve/webhooks")
  async getAllWebhooks(
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset: number,
  ) {
    console.log("Getting all webhooks");
    return await this.quicknodeService.getAllWebhooks(limit, offset);
  }

  // get webhook by id 
  @Get("retrieve/webhook/:id")
  async getWebhookById(@Param('id') id: string ) {
    console.log("Getting webhook by id", id);
    return await this.quicknodeService.getWebhookById(id);
  }

  // update existing webhook 
  @Patch("update/webhook/:id")
  async updateWebhook(@Param('id') id: string, @Body() dto: UpdateQnWebhookDto) {
    console.log("Updating webhook by id", id);
    return await this.quicknodeService.updateWebhook(id, dto);
  }

  // delete webhook by id 
  @Delete("delete/webhook/:id")
  async deleteWebhook(@Param('id') id: string) {
    console.log("Deleting webhook by id", id);
    return await this.quicknodeService.deleteWebhook(id);
  }

  // activate webhook by id 
  @Post("activate/webhook/:id")
  async activateWebhook(@Param('id') id: string, @Body('startFrom') startFrom: string = 'latest'){
    console.log("Activating webhook by id", id);
    return await this.quicknodeService.activateWebhook(id, startFrom),"Webhook Activated";
  }

  //pause webhook by id 
  @Post("paused/webhook/:id")
  async pausedWebhook(@Param('id') id: string){
    console.log("Pausing webhook by id", id);
    return await this.quicknodeService.pauseWebhook(id),"Webhook Paused";
  }

  //test filter 
  @Post("filter/webhook/")
  async testFilter(@Body() dto: FilterQnWebhookDto){
    console.log("testing filter ")
    return await this.quicknodeService.testFilter(dto);
  }

  // ==================== W STREAMS METHODS ====================

  // Create basic stream
  @Post('create/stream')
  async createStream(@Body() dto: CreateQnStreamDto) {
    console.log("Creating Quicknode stream");
    return await this.quicknodeService.createStream(dto);
  }

  // Get all streams
  @Get("retrieve/streams")
  async getAllStreams(
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset: number,
  ) {
    return await this.quicknodeService.getAllStreams(limit, offset);
  }

   // Get stream by ID
  @Get("retrieve/stream/:id")
  async getStreamById(@Param('id') id: string) {
    console.log("Getting stream by id", id);
    return await this.quicknodeService.getStreamById(id);
  }

  // Update existing stream
  @Patch("update/stream/:id")
  async updateStream(@Param('id') id: string, @Body() dto: UpdateQnStreamDto) {
    console.log("Updating stream by id", id);
    return await this.quicknodeService.updateStream(id, dto);
  }

   // Delete stream by ID
  @Delete("delete/stream/:id")
  async deleteStream(@Param('id') id: string) {
    console.log("Deleting stream by id", id);
    return await this.quicknodeService.deleteStream(id);
  }

  // Activate stream by ID ( latest / last )
  @Post("activate/stream/:id")
  async activateStream(@Param('id') id: string, @Body('startFrom') startFrom: string = 'latest') {
    console.log("Activating stream by id", id);
    return await this.quicknodeService.activateStream(id, startFrom);
  }

  // Pause stream by ID
  @Post("pause/stream/:id")
  async pauseStream(@Param('id') id: string) {
    console.log("Pausing stream by id", id);
    return await this.quicknodeService.pauseStream(id);
  }

  // Test stream filter
  @Post("test/filter/stream")
  async testStreamFilter(@Body() dto: FilterQnStreamDto) {
    console.log("Testing stream filter");
    return await this.quicknodeService.testStreamFilter(dto);
  }

  // Delete all streams 
  @Delete("delete/streams/all")
  async deleteAllStreams() {
    console.log("⚠️ DANGER: Deleting ALL streams");
    return await this.quicknodeService.deleteAllStreams();
  }









}
