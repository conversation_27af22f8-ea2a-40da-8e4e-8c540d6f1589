import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { firstValueFrom, isObservable, Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { UpdateQnWebhookDto } from './dto/webhooks/update-qn-webhook.dto';
import { FilterQnWebhookDto } from './dto/webhooks/filter-qn-webhook.dto';
import configuration from 'config/configuration';
import { CreateQnStreamDto } from './dto/streams/create-qn-stream.dto';
import { UpdateQnStreamDto } from './dto/streams/update-qn-stream.dto';
import { FilterQnStreamDto } from './dto/streams/filter-qn-streams.dto';

@Injectable()
export class QuicknodeService {
  private readonly logger = new Logger(QuicknodeService.name);
  private readonly baseUrl = 'https://api.quicknode.com/webhooks/rest/v1';
  private readonly streamBaseUrl = 'https://api.quicknode.com/streams/rest/v1';
  private readonly apiKey: string; 

  constructor(private readonly HttpService: HttpService) {
    this.apiKey = configuration().qn_api_key;
    if (!this.apiKey) {
      console.error('❌ QN_API_KEY is undefined - configuration not loading properly');
      throw new Error('QN_API_KEY is not defined');
    } else {
      console.log('✅ QN_API_KEY loaded successfully');
    }
  }

  private getHeaders() {
    return {
      'x-api-key': this.apiKey,
      'Content-Type': 'application/json',
    };
  }

  // lets make a service just for this ask kraken on opinion 
  protected async handleRequest<T>(
    request: Observable<AxiosResponse<T>> | Promise<AxiosResponse<T>>, // data kena error 
    operation: string
  ): Promise<T> {
    try {
      const response = isObservable(request)
        ? await firstValueFrom(request)
        : await request;

      this.logger.log(`YYYIPPPEEE ${operation} successful`);
      return response.data;
    } catch (error) {
      const status = error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
      const message = error.response?.data || `${operation} failed`;

      this.logger.error(`NEEIINN ${operation} failed:`, message);
      throw new HttpException(message, status);
    }
  }

  // ==================== D WEBHOOK METHODS ====================

  // create webhook 
  async createWebhook(name: string, network: string, notification_email: string, webhookUrl: string) {
    const webhookData =  {
      name,
      network,
      notification_email,
      destination_attributes: {
        url: webhookUrl,
        compression: 'none'
      },
      status: 'active',
    }

    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks`,
      webhookData,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Create new webhook');
  }

  async createWebhookWithTemplate(name: string , network: string, notification_email: string, webhookUrl: string, template: string, wallets: string[], filter_function: string) {
    const webhookData = {
      name,
      network,
      notification_email: notification_email,
      destination_attributes: {
        url: webhookUrl,
        compression: 'none'
      },
      filter_function: filter_function,
      status: 'active',
      templateArgs: {
        wallets: wallets
      },
    };

    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks/template/${template}`,
      webhookData,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Create new webhook with template');
  }

  async getAllWebhooks(limit: number, offset: number){
    const request = this.HttpService.get(
    `${this.baseUrl}/webhooks?limit=${limit}&offset=${offset}`,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Get all webhooks');
  }

  async getWebhookById(id: string) {
    const request = this.HttpService.get(
      `${this.baseUrl}/webhooks/${id}`,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Get webhook by id');
  }

  async updateWebhook(id: string, dto: UpdateQnWebhookDto) {
    const request = this.HttpService.patch(
      `${this.baseUrl}/webhooks/${id}`,
      dto,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Get webhook by id' ,);
  }

  async deleteWebhook(id: string ){
    const request = this.HttpService.delete(
      `${this.baseUrl}/webhooks/${id}`,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request," Delete webhook by id")
  }

  async activateWebhook(id: string, startFrom = 'latest') {
    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks/${id}/activate`,
      {startFrom},
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Activate webhook by id');
  }

  async pauseWebhook(id: String) {
    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks/${id}/pause`,
      {},
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Pause webhook by id');
  }

  async testFilter(dto : FilterQnWebhookDto) {
    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks/test_filter`,
      dto,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Test filter');
  }

  // ==================== W STREAMS METHODS ====================

  async createStream(dto: CreateQnStreamDto) {
    const request = this.HttpService.post(
      `${this.streamBaseUrl}/streams`,
      dto,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Create new stream');
  }

  async getAllStreams(limit: number, offset: number) {
    const request = this.HttpService.get(
      `${this.streamBaseUrl}/streams?limit=${limit}&offset=${offset}`,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Get all streams');
  }

  // Get stream by ID
  async getStreamById(id: string) {
    const request = this.HttpService.get(
      `${this.streamBaseUrl}/streams/${id}`,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Get stream by id');
  }

  // Update stream
  async updateStream(id: string, dto: UpdateQnStreamDto) {
    const request = this.HttpService.patch(
      `${this.streamBaseUrl}/streams/${id}`,
      dto,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Update stream by id');
  }

  // Delete stream by ID
  async deleteStream(id: string) {
    const request = this.HttpService.delete(
      `${this.streamBaseUrl}/streams/${id}`,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Delete stream by id');
  }

  // Activate stream ( latest / last )
  async activateStream(id: string, startFrom = 'latest') {
    const request = this.HttpService.post(
      `${this.streamBaseUrl}/streams/${id}/activate`,
      { startFrom },
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Activate stream by id');
  }

  // Pause stream
  async pauseStream(id: string) {
    const request = this.HttpService.post(
      `${this.streamBaseUrl}/streams/${id}/pause`,
      {},
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Pause stream by id');
  }

   // Test stream filter
  async testStreamFilter(dto: FilterQnStreamDto) {
    const request = this.HttpService.post(
      `${this.streamBaseUrl}/streams/test_filter`,
      dto,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Test stream filter');
  }

  // Delete all streams (DANGEROUS)
  async deleteAllStreams() {
    const request = this.HttpService.delete(
      `${this.streamBaseUrl}/streams`,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Delete all streams');
  }

}
