import { Injectable, Logger } from "@nestjs/common";
import { InjectEntityManager } from "@nestjs/typeorm";
import { MonitoredAddress } from "src/webhooks/entities/monitored-adddresses.entity";
import { EntityManager } from "typeorm";
import { Webhook } from "./entities/webhook.entity";
import { Network } from "src/networks/entities/network.entity";
import * as crypto from 'crypto';
import axios from "axios";

interface TransactionData {
  hash: string;
  from: string;
  to: string;
  value: string;
  blockNumber: string;
  blockTimestamp: string;
  gasPrice?: string;
  transactionIndex?: string;
  nonce?: string;
}

interface WebhookDeliveryResult {
  success: boolean;
  webhooksFound: number;
  webhooksDelivered: number;
  webhooksFailed: number;
  message: string;
  details?: any;
}

@Injectable()
export class WebhookDeliveryService {
  private readonly logger = new Logger(WebhookDeliveryService.name);

  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager
  ) {}

  // generate signature 
  private generateSignature(payload: string, secret: string): string {
    return crypto
      .createHmac('sha256', secret)
      .update(payload, 'utf8')
      .digest('hex');
  }

  async extractAddresses (transactions: TransactionData[]): Promise<string[]> {
    const addresses = new Set<string>();
    transactions.forEach(tx => {
      if (tx.from) addresses.add(tx.from.toLowerCase());
      if (tx.to) addresses.add(tx.to.toLowerCase());
    });
    return Array.from(addresses);
  }

  async findActiveWebhooks(networkId: number, addresses: string[]): Promise<any> {
    // console.log("addresses:", addresses , networkId );
    // to find all unique webhooks monitoring which addresses 
    const result = await this.entityManager
      .createQueryBuilder(Webhook, 'w')
      .innerJoin(MonitoredAddress, 'ma', 'ma.webhook_id = w.id')
      .select('w.id', 'webhook_id')
      .addSelect('w.webhook_url', 'webhook_url')
      .addSelect('w.security_tokens', 'security_tokens')
      .addSelect('array_agg(ma.address)', 'monitored_addresses')
      .where('ma.is_active = true')
      .andWhere('w.status = :status', { status: 'active' })
      .andWhere('w.network_id = :networkId', { networkId })
      .andWhere('w.deleted_at IS NULL')
      .andWhere('ma.address IN (:...addresses)', { addresses })
      .groupBy('w.id')
      .addGroupBy('w.webhook_url')
      .addGroupBy('w.security_tokens')
      .getRawMany();
    return result;
  }

  async filterTransactionsForWebhook(transactions: TransactionData[], monitoredAddresses: any): Promise<any> {
    // normalize n store in set for better indexing
    if (!Array.isArray(monitoredAddresses)) {
      console.warn('monitoredAddresses is not an array:', monitoredAddresses);
      return [];
    }
    const addressSet = new Set(monitoredAddresses.map(addr => addr.toLowerCase()));
    
    return transactions.filter(tx => {
      const fromMatch = tx.from && addressSet.has(tx.from.toLowerCase());
      const toMatch = tx.to && addressSet.has(tx.to.toLowerCase());
      // keep only transactiosn that matches
      return fromMatch || toMatch;
    });
  }
  

  async sendWebhook(webhook: Webhook, transactions: TransactionData[], networkName: string): Promise<any> {
    const payload = {
      webhook_id: webhook.id,
      network: networkName,
      transactions,
      timestamp: new Date().toISOString(),
    };

    const payloadString = JSON.stringify(payload);
    const signature = this.generateSignature(payloadString, webhook.security_tokens);
    try {
      const response = await axios.post(webhook.webhook_url, payload, {
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Signature': `sha256=${signature}`,
          'User-Agent': 'Wallet-Scanner-Webhook',   //  used to identify your webhook delivery service
        },
        timeout: 10_000, //await 10 seconds for reply back [ 200 , 404 bad request etc ]
      });

      return { success: response.status >= 200 && response.status < 300, status: response.status }; // expect return response.ok;
    } catch (error: any) {
      this.logger.error(
        `Webhook delivery error [${webhook.id}] → ${webhook.webhook_url}:`, error.message,
      );
      return { success: false, error: error.message };
    }
  }



  // ===================== Main Method =====================

  // Main function to route transactions to matching webhooks
  async routeTransactionsToWebhooks(networkId: number, transactions: TransactionData[], networkName: string): Promise<WebhookDeliveryResult> {
    // Step 1: Validate input ( if got data )
     if (!transactions || transactions.length === 0) {
      return {
        success: true,
        webhooksFound: 0,
        webhooksDelivered: 0,
        webhooksFailed: 0,
        message: 'No transactions to process'
      };
    }

    // Step 2: Extract addresses from transactions
    const addresses = await this.extractAddresses(transactions);
    // console.log("available addresses:", addresses); // logger ???

    // Step 3: Find matching webhooks
    const webhooks = await this.findActiveWebhooks(networkId, addresses);
    // console.log("webhooks:", webhooks); 

    if (webhooks.length === 0) {
      return {
        success: true, // Not an error - just no webhooks found
        webhooksFound: 0,
        webhooksDelivered: 0,
        webhooksFailed: 0,
        message: `No active webhooks found monitoring these addresses on ${networkName}`,
        details: { addresses, networkName }
      };
    }

    // Step 4: Send to each webhook
    let delivered = 0;
    let failed = 0;

    for (const webhook of webhooks) {
      const relevantTxs = await this.filterTransactionsForWebhook(transactions, webhook.monitored_addresses);
      // console.log('Returning all relevant transactions')
      // console.log(relevantTxs)

      if (relevantTxs.length === 0) continue;

      const success = await this.sendWebhook(webhook, relevantTxs, networkName);
      if (success) {
        delivered++;
        this.logger.log(`Delivered ${relevantTxs.length} transactions to webhook ${webhook.webhook_id}`);
      } else {
        failed++;
        this.logger.error(`Failed to deliver to webhook ${webhook.webhook_id}`);
      }
    }

    // Step 5: Return final results 🫣
    const allSuccessful = failed === 0; 

    return {
      success: allSuccessful,
      webhooksFound: webhooks.length,
      webhooksDelivered: delivered,
      webhooksFailed: failed,
      message: allSuccessful 
        ? `Successfully delivered to ${delivered} webhooks`
        : `Delivered to ${delivered} webhooks, ${failed} failed`
    };
  }

  // Get addresses by id
  async getWebhookAddresses(webhookId: string): Promise<string[]> {
    const addresses = await this.entityManager
      .createQueryBuilder(MonitoredAddress, 'ma')
      .select('ma.address')
      .where('ma.webhook_id = :webhookId AND ma.is_active = true AND ma.webhook_id IS NOT NULL', { webhookId })
      .getRawMany();

    return addresses.map(row => row.address).filter(addr => addr != null).map(addr => addr.toLowerCase());
  }

  // Get all Active webhooks
  async getAllActiveWebhooks(networkId: number): Promise<any> {
    return await this.entityManager
      .createQueryBuilder(Webhook, 'w')
      .where('w.network_id = :networkId AND w.status = :status', { networkId, status: 'active' })
      .getMany();
  }

  // create fake transactions for weburl testing  
  async testDelivery(networkId: number, networkName: string): Promise<WebhookDeliveryResult> {
    const testTransactions: TransactionData[] = [
      {
        hash: "0xtest123",
        from: "0x31384E21D3df6F69DB15859DBE0e130ceab2398e",
        to: "0xEd7923a4F881F26C3d76ed17D28cc44dDa5Cd4C8",
        value: "0x58d15e17628000",
        blockNumber: "0x162428c",
        blockTimestamp: "0x68aae1af"
      }
    ];

    return await this.routeTransactionsToWebhooks(networkId, testTransactions, networkName);
  }







}

