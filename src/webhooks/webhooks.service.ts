import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { CreateWebhookDto } from './dto/create-webhook.dto';
import { UpdateWebhookDto } from './dto/update-webhook.dto';
import { EntityManager } from 'typeorm';
import { InjectEntityManager } from '@nestjs/typeorm';
import * as crypto from 'crypto';
import { Webhook } from './entities/webhook.entity';
import { User } from 'src/users/entities/user.entity';
import { NetworksService } from 'src/networks/networks.service';
import { ApiKeyService } from 'src/api-key/api-key.service';
import { Network } from 'src/networks/entities/network.entity';
import {WebhookActiveStatus, WebhookStatus } from './webhooks.status.enum';
import { MonitoredAddress } from 'src/webhooks/entities/monitored-adddresses.entity';
import { isUUID } from 'class-validator';
import { QuicknodeService } from 'src/quicknode/quicknode.service';
import { QuickNodeTemplate } from 'src/quicknode/dto/webhooks/create-qn-webhook-template.dto';
import { QuicknodeWebhook, QuicknodeWebhookStatus } from './entities/quicknode-webhook.entity';
import { filter } from 'rxjs';

@Injectable()
export class WebhooksService {
  private readonly MAX_ADDRESSES_PER_WEBHOOK = 1000;
  private readonly logger = new Logger(WebhooksService.name);

  constructor( 
    @InjectEntityManager()
    private entityManager : EntityManager,
    private networksService: NetworksService,
    private apiKeyService: ApiKeyService,
    private quicknodeService: QuicknodeService,
  ){}

  // ==================== HELPER METHODS ====================

  async generateSecurityToken(): Promise<string> {
    const prefix = 'wsLAF_';
    const token = crypto.randomBytes(32).toString('base64');
    return `${prefix}${token}`;
  }

  async checkIfWebhookExists(id: string): Promise<boolean> {
    if (!id) {
      throw new BadRequestException('Invalid webhook ID');
    }
    
    const webhook = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .where('webhook.id = :id AND webhook.deleted_at IS NULL', { id })
      .getOne();
    
    if (!webhook) {
      throw new BadRequestException(`Webhook with ID ${id} not found`);
    }
    return true;
  }

  async validateWebhookOwnership(user_id: number, webhook_name: string){
    const validateWebhookName = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .where('webhook.user_id = :user_id AND webhook.name = :webhook_name', {
        user_id,
        webhook_name,
      })
      .getOne();

    if (validateWebhookName) {
      throw new BadRequestException('Webhook name already exists for this user');
    }
  }

  async validateAddressLimit(addresses: string[]): Promise<void> {
    if (addresses && addresses.length > this.MAX_ADDRESSES_PER_WEBHOOK) {
      throw new BadRequestException(
        `Address limit exceeded. Maximum ${this.MAX_ADDRESSES_PER_WEBHOOK} addresses allowed per webhook. ` +
        `You provided ${addresses.length} addresses. Please create a new webhook for additional addresses.`
      );
    }
  }

  private async handleQuicknodeWebhook(network: Network, newAddresses: string[]): Promise<void> {
    try {
      // Check if QuickNode webhook exists for this network
      let existingQuicknodeWebhook = await this.entityManager
        .createQueryBuilder(QuicknodeWebhook, 'qw')
        .where('qw.network_id = :network_id', { network_id: network.id })
        .andWhere('qw.deleted_at IS NULL')
        .andWhere('qw.status = :status', { status: QuicknodeWebhookStatus.ACTIVE })
        .getOne();

      if (!existingQuicknodeWebhook) {
        // Create new QuickNode webhook
        await this.createNewQuicknodeWebhook(network, newAddresses);
      } else {
        // Update existing QuickNode webhook
        await this.updateExistingQuicknodeWebhook(existingQuicknodeWebhook, network.id, newAddresses);
      }
      
    } catch (error) {
      this.logger.error(`QuickNode webhook handling failed: ${error.message}`);
      throw new BadRequestException(`QuickNode webhook operation failed: ${error.message}`);
    }
  }

  private async createNewQuicknodeWebhook(network: Network, addresses: string[]): Promise<void> {
    this.logger.log("Creating new QuickNode webhook for network:", network.display_name);
    
    const webhookName = `${network.display_name}-Webhook-1`;
    const destinationUrl = this.buildDestinationUrl(network.id);

    // Create webhook in QuickNode
    const quicknodeResponse = await this.quicknodeService.createWebhookWithTemplate(
      webhookName,
      network.display_name,
      '<EMAIL>',
      destinationUrl,
      QuickNodeTemplate.EVM_WALLET_FILTER,   // fix this 
      addresses,
      this.getFilterFunction()
    );

    // Save QuickNode webhook record in our database
    await this.entityManager
      .createQueryBuilder()
      .insert()
      .into(QuicknodeWebhook)
      .values({
        network_id: network.id,
        qn_webhook_id: quicknodeResponse.id,
        webhook_name: webhookName,
        destination_url: destinationUrl,
        security_token: quicknodeResponse.destination_attributes.security_token,
        status: QuicknodeWebhookStatus.ACTIVE,
        address_count: addresses.length,
        max_address_capacity: 1000
      })
      .execute();

    this.logger.log(`Created QuickNode webhook: ${quicknodeResponse.id}`);
  }

  private async updateExistingQuicknodeWebhook(
    quicknodeWebhook: QuicknodeWebhook, 
    network_id: number, 
    newAddresses: string[]
  ): Promise<void> {
    this.logger.log("Updating existing QuickNode webhook:", quicknodeWebhook.qn_webhook_id);
    
    // Get all current addresses for this network from all active webhooks
    const allCurrentAddresses = await this.entityManager
      .createQueryBuilder(MonitoredAddress, 'ma')
      .innerJoin(Webhook, 'w', 'w.id = ma.webhook_id')
      .where('w.network_id = :network_id', { network_id })
      .andWhere('ma.is_active = true')
      .andWhere('w.deleted_at IS NULL')
      .andWhere('w.status = :status', { status: 'active' })
      .select('ma.address')
      .getRawMany()
      .then(results => results.map(row => row.address.toLowerCase()));
    
    // Combine with new addresses (remove duplicates)
    const combinedAddresses = [...new Set([...allCurrentAddresses, ...newAddresses])];
    
    // Check capacity limit
    if (combinedAddresses.length > quicknodeWebhook.max_address_capacity) {
      throw new BadRequestException(
        `Combined addresses (${combinedAddresses.length}) exceed capacity (${quicknodeWebhook.max_address_capacity}). ` +
        `Need to create additional QuickNode webhook.`
      );
    }

    // Update QuickNode webhook with combined addresses
    await this.quicknodeService.updateWebhook(
      quicknodeWebhook.qn_webhook_id,
      {
        templateArgs: {
          wallets: combinedAddresses
        }
      } as any
    );

    // Update address count in our database
    await this.entityManager
      .createQueryBuilder()
      .update(QuicknodeWebhook)
      .set({ address_count: combinedAddresses.length })
      .where('id = :id', { id: quicknodeWebhook.id })
      .execute();

    this.logger.log(`Updated QuickNode webhook with ${combinedAddresses.length} total addresses`);
  }

  private buildDestinationUrl(network_id: number): string {
    return `${process.env.BASE_URL || 'https://scanner.cake5x.com'}/transaction-router/receive/${network_id}`;
  }

  private getFilterFunction(){
    const filterFunction = `
      function main(payload) {
        const addresses = Array.isArray(tempArgs?.addresses) ? tempArgs.addresses : [];
  const wallets = new Set(addresses);
        const TransactionData = [];

        for (const block of payload.data) {
          const transactions = block.block?.transactions || [];
          const receipts = block.receipts || [];
          const blockTimestamp = block.block.timestamp;

          // Create receipt lookup map
          const receiptMap = new Map();
          receipts.forEach(receipt => {
            receiptMap.set(receipt.transactionHash, receipt);
          });

          // Filter and merge transaction + receipt into single objects
          for (const tx of transactions) {
            const from = tx.from?.toLowerCase();
            const to = tx.to?.toLowerCase();
            const value = tx.value || '0';

            if (value !== '0' && (wallets.has(from) || wallets.has(to))) {
              const receipt = receiptMap.get(tx.hash);
              
              // Merge transaction and receipt into one object
              const mergedData = {
                blockTimestamp,
                ...tx,
                ...receipt,

              };
              
              TransactionData.push(mergedData);
            }
          }
        }

        return TransactionData.length > 0 ? { TransactionData } : null;
      }
    `
    return this.encodeWithBase64(filterFunction);
  }

  private encodeWithBase64(filterFunction: string): string {
    return Buffer.from(filterFunction).toString("base64");
  }



  // ==================== MONITORED ADDRESSES METHODS ====================

  // Save addresses to monitored_addresses table
  async saveMonitoredAddresses(webhookId: string, userId: number, addresses: string[]): Promise<void> {
    if (!addresses || addresses.length === 0) {
      return;
    }

    const monitoredAddresses = addresses.map(address => ({
      user_id: userId,
      webhook_id: webhookId,
      address: address.toLowerCase(), // normalize them so db indexing doesnt 💥 up 
      is_active: true
    }));

    await this.entityManager
      .createQueryBuilder()
      .insert()
      .into(MonitoredAddress)
      .values(monitoredAddresses)
      .execute();
  }

  // Get all monitored addresses 
  async getMonitoredAddresses(webhookId: string): Promise<string[]> {
    this.logger.log(`getMonitoredAddresses webhookId: ${webhookId}`);

    try {
      if (!isUUID(webhookId)) {
        this.logger.warn(`Invalid webhookId provided: ${webhookId}`);
        throw new BadRequestException(`Invalid webhookId: must be a valid UUID`);
      }

      const addresses = await this.entityManager
        .createQueryBuilder(MonitoredAddress, 'ma')
        .where('ma.webhook_id = :webhookId', { webhookId })
        .andWhere('ma.is_active = true')
        .select('ma.address', 'address')
        .getRawMany();

      if (!addresses || addresses.length === 0) {
        this.logger.warn(`No active monitored addresses found for webhookId: ${webhookId}`);
        return [];
      }

      return addresses.map(row => row.address);
    } catch (error) {
      this.logger.error(
        `Failed to fetch monitored addresses for webhookId: ${webhookId}`,
        error.stack || error.message,
      );
      throw error;

    }
  }

  // Update monitored addresses  
  async updateMonitoredAddresses(webhookId: string, userId: number, newAddresses: string[]): Promise<void> {
    // blow it up like hiroshima and colonize like christopher columbus 
    await this.entityManager
      .createQueryBuilder()
      .update(MonitoredAddress)
      .set({ is_active: false })
      .where('webhook_id = :webhookId', { webhookId })
      .execute();

    // Add new addresses
    await this.saveMonitoredAddresses(webhookId, userId, newAddresses);
  }

  // ==================== WEBHOOK METHODS ====================

  // must be guarded by api key rmb that 
  async createWebhook(createWebhookDto: CreateWebhookDto , user_id: number): Promise<any> {
    console.log("createWebhookDto", createWebhookDto)

    try {
      // 1. check if network is valid
      const network = await this.networksService.findOneWithName(createWebhookDto.network);

      // 2. check if user has a webhook with this name
      await this.validateWebhookOwnership(user_id, createWebhookDto.name);

      // 3. check if address limit is exceeded
      await this.validateAddressLimit(createWebhookDto.wallet_addresses);

      const currentBlockNumber = await this.networksService.getLatestSequence(createWebhookDto.network);
      const securityToken = await this.generateSecurityToken();

      const user = await this.entityManager
        .createQueryBuilder(User, 'user')
        .where('user.id = :user_id', { user_id: user_id })
        .getOne();

      if (!user) {
        throw new BadRequestException('User not found');
      }

      // 5. Create internal webhook record
      const insertResult = await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(Webhook)
        .values({
          user_id: user_id,
          network_id: network.id,
          name: createWebhookDto.name,
          webhook_url: createWebhookDto.destination_attributes.url,
          security_tokens: securityToken,
          start_position: currentBlockNumber,
          status: createWebhookDto.status as any, 
        })
        .returning('*')
        .execute();

        const savedWebhook = insertResult.generatedMaps[0] as Webhook;
        
        // 6. Save monitored addresses
        await this.saveMonitoredAddresses(
          savedWebhook.id, 
          user_id, 
          createWebhookDto.wallet_addresses || []
        );

        await this.handleQuicknodeWebhook(network, createWebhookDto.wallet_addresses || []);

        const destinationAttributes = {
          url: createWebhookDto.destination_attributes.url,
          security_token: securityToken,
        };

        const monitorAddresses = {
          wallets: createWebhookDto.wallet_addresses || [],
        };

        return {
        id: savedWebhook.id,
        name: savedWebhook.name,
        status: savedWebhook.status,
        created_at: savedWebhook.created_at,
        destination_attributes: destinationAttributes,
        network: createWebhookDto.network,
        notification_email: user.email,
        sequence: currentBlockNumber,
        updated_at: savedWebhook.updated_at,
        monitorAddresses: monitorAddresses
      };
    } catch (error) {
      throw new BadRequestException("Failed to create webhook", error.message);
    }
  }

  async getAllWebhooks(user_id: number , limit: number, offset: number): Promise<{ data: any[]; pageInfo: any }> {
    const totalCount = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .where('webhook.user_id = :user_id AND webhook.deleted_at IS NULL', { user_id })
      .getCount();

    const webhooks = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .leftJoin(Network, 'network', 'network.id = webhook.network_id')
      .where('webhook.user_id = :user_id AND webhook.deleted_at IS NULL', { user_id })
      .orderBy('webhook.created_at', 'DESC')
      .select([
        'webhook.id AS webhook_id',
        'webhook.name AS name',
        'webhook.status AS status',
        'webhook.created_at AS created_at',
        'webhook.updated_at AS updated_at',
        'webhook.webhook_url AS webhook_url',
        'webhook.security_tokens AS security_tokens',
        'webhook.start_position AS start_position',
        'network.display_name AS network_display_name',
      ])
      .limit(limit)
      .offset(offset)
      .getRawMany();


      //await cannot work in map func that why need async func
      const data = await Promise.all(webhooks.map(async (webhook) => {
        let sequence: number;

        
      if (webhook.status === WebhookStatus.ACTIVE) {
        sequence =  await this.networksService.getLatestSequence(webhook.network_display_name) || 0;
      } else if (webhook.status === WebhookStatus.PAUSED) {
        sequence = webhook.start_position ? webhook.start_position : 0;
      } else { // any other state
        sequence = webhook.start_position ? webhook.start_position : 0;
      }

      const addresses = await this.getMonitoredAddresses(webhook.webhook_id);
      const monitorAddresses = {
        wallets: addresses,
      };

      return {
        id: webhook.webhook_id, 
        name: webhook.name,
        status: webhook.status,
        created_at: webhook.created_at,
        destination_attributes: {
          url: webhook.webhook_url,
          security_token: webhook.security_tokens,
        },
        network: webhook.network_display_name,
        sequence: sequence,
        monitorAddresses: monitorAddresses,
        updated_at: webhook.updated_at,
      };
    }));

    return {
      data,
      pageInfo: {
        limit,
        offset,
        total: totalCount, 
      },
    }
  }

  async getWebhookById(id: string): Promise<any> {
    const webhook = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .leftJoin(Network, 'network', 'network.id = webhook.network_id')
      .where('webhook.id = :id AND webhook.deleted_at IS NULL', { id })
      .select([
        'webhook.id AS webhook_id',
        'webhook.name AS name',
        'webhook.status AS status',
        'webhook.created_at AS created_at',
        'webhook.updated_at AS updated_at',
        'webhook.webhook_url AS webhook_url',
        'webhook.security_tokens AS security_tokens',
        'network.display_name AS network_display_name',
      ])
      .getRawOne();

    const currentBlockNumber = await this.networksService.getLatestSequence(webhook.network_display_name);
    const addresses = await this.getMonitoredAddresses(webhook.webhook_id);
    const monitorAddresses = {
      wallets: addresses,
    };

    return {
      id: webhook.webhook_id, 
      name: webhook.name,
      status: webhook.status,
      created_at: webhook.created_at,
      destination_attributes: {
        url: webhook.webhook_url,
        security_token: webhook.security_tokens,
      },
      network: webhook.network_display_name,
      sequence: currentBlockNumber,
      monitorAddresses: monitorAddresses,
      updated_at: webhook.updated_at,
    };
  }

  async updateWebookById(id: string, updateWebhookDto: UpdateWebhookDto): Promise<any> {
    try {
      await this.checkIfWebhookExists(id);
      // check if paused 

      const updateData: any = {};
      if (updateWebhookDto.name) {
        updateData.name = updateWebhookDto.name;
      }
      
      if (updateWebhookDto.destination_attributes?.url) {
        updateData.webhook_url = updateWebhookDto.destination_attributes.url;
      }
      
      if (updateWebhookDto.status) {
        updateData.status = updateWebhookDto.status;
      }

      // Note: wallet addresses are now handled through monitored_addresses table

      const updateResult = await this.entityManager
        .createQueryBuilder()
        .update(Webhook)
        .set(updateData)
        .where('id = :id', { id })
        .returning('*')
        .execute();

      const updatedWebhook = updateResult.raw[0] as Webhook;

      // Update monitored addresses if provided
      if (updateWebhookDto.monitorAddresses?.wallets) {
        await this.updateMonitoredAddresses(
          id, 
          updatedWebhook.user_id, 
          updateWebhookDto.monitorAddresses.wallets
        );
      }

      // Get addresses 
      const addresses = await this.getMonitoredAddresses(id);
      const monitorAddresses = {
        wallets: addresses,
      };


      return {
        id: updatedWebhook.id, 
        name: updatedWebhook.name,
        status: updatedWebhook.status,
        created_at: updatedWebhook.created_at,
        destination_attributes: {
          url: updatedWebhook.webhook_url,
          security_token: updatedWebhook.security_tokens,
        },
        // dont allow update network
        sequence: updatedWebhook.start_position,  // on pause save current block
        monitorAddresses: monitorAddresses,
        updated_at: updatedWebhook.updated_at,
      };



    }catch (error) {
      throw new BadRequestException("Failed to update webhook", error.message);
    }
  }

  async deleteWebhookById(id: string): Promise<any> {
    try {
      await this.checkIfWebhookExists(id);

      await this.entityManager // soft delete ???????
        .createQueryBuilder()
        .update(MonitoredAddress)
        .set({ is_active: false })
        .where('webhook_id = :id', { id })
        .execute();

      await this.entityManager
        .createQueryBuilder()
        .update(Webhook)
        .set({ status: WebhookStatus.TERMINATED })
        .where('id = :id', { id })
        .execute();

      await this.entityManager
        .createQueryBuilder(Webhook, 'webhook')
        .where('id = :id', { id })
        .softDelete()
        .execute();

      return { message: 'Webhook deleted successfully' };
    }catch(error){
      throw new BadRequestException("Failed to delete webhook", error.message);
    }
  }

  async pauseWebhook(id: string): Promise<any> {
    try {
      await this.checkIfWebhookExists(id);
      // saving current blocknumber in start_position
      const webhook = await this.entityManager
        .createQueryBuilder(Webhook, 'webhook')
        .leftJoin(Network, 'network', 'network.id = webhook.network_id')
        .where('webhook.id = :id AND webhook.deleted_at IS NULL', { id })
        .select(['network.display_name AS network_display_name'])
        .getRawOne();

      const currentBlockNumber = await this.networksService.getLatestSequence(webhook.network_display_name);

      // chnaging satus to paused
      await this.entityManager
        .createQueryBuilder()
        .update(Webhook)
        .set({ start_position: currentBlockNumber , status: WebhookStatus.PAUSED })
        .where('id = :id', { id })
        .returning('*')
        .execute();

      return { message: 'Webhook paused successfully' };
    }catch(error){
      throw new BadRequestException("Failed to pause webhook", error.message);
    }
  }

  async activateWebhook(id: string , startFrom: string): Promise<any> {
    try {
      await this.checkIfWebhookExists(id);
      // get current block number and start position
      const webhook = await this.entityManager
        .createQueryBuilder(Webhook, 'webhook')
        .leftJoin(Network, 'network', 'network.id = webhook.network_id')
        .where('webhook.id = :id', { id })
        .select(['webhook.start_position', 'network.display_name AS network_display_name'])
        .getRawOne();

      const currentBlockNumber = await this.networksService.getLatestSequence(webhook.network_display_name);

      let startPosition = 0;

      // if last / latest
      if (startFrom === WebhookActiveStatus.LAST) {
        startPosition = webhook.start_position;
      }
      else if (startFrom === WebhookActiveStatus.LATEST) {
        startPosition = currentBlockNumber;
      }
      else {
        throw new BadRequestException("Invalid startFrom value");
      }

      // save as active and save new start position
      await this.entityManager
        .createQueryBuilder()
        .update(Webhook)
        .set({ start_position: startPosition , status: WebhookStatus.ACTIVE })
        .where('id = :id', { id })
        .returning('*')
        .execute();

      return { message: 'Webhook activated successfully' };

    }catch(error){
      throw new BadRequestException("Failed to activate webhook", error.message);
    }
  }

  async deleteAllUserWebhooks(user_id: number): Promise<any> {
    try {
      // deactivate all addresses 4 dis webhook
      await this.entityManager
        .createQueryBuilder()
        .update(MonitoredAddress)
        .set({ is_active: false })
        .where('user_id = :user_id', { user_id })
        .execute();

      await this.entityManager
        .createQueryBuilder()
        .update(Webhook)
        .set({ status: WebhookStatus.TERMINATED })
        .where('user_id = :user_id', { user_id })
        .execute();
      
        await this.entityManager
        .createQueryBuilder(Webhook, 'webhook')
        .where('user_id = :user_id', { user_id })
        .softDelete()
        .execute();

      return { message: 'All user webhooks deleted successfully' };
    }catch(error){
      throw new BadRequestException("Failed to delete all user webhooks", error.message);
    }
  }

}
