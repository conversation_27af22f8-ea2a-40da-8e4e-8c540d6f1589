import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsEnum, IsInt, IsNotEmpty, IsObject, IsOptional, IsString, ValidateNested } from "class-validator";
import { WebhookStatus } from "../webhooks.status.enum";

class DestinationAttributesDto {
  @ApiProperty({ example: "https://scanner.cake5x.com/transaction-router/receive/1" })
  @IsNotEmpty()
  @IsString()
  url: string;
}

export class CreateWebhookDto {
 @ApiProperty({ example: "banana rama"})
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ example: "ethereum-mainnet"})
  @IsNotEmpty()
  @IsString()
  network: string;

  @ApiProperty({
    example: {
      url: "https://scanner.cake5x.com/transaction-router/receive/1",
      // compression: "none"
    }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DestinationAttributesDto)
  destination_attributes: DestinationAttributesDto;

  @ApiProperty({ example: WebhookStatus.ACTIVE, enum: WebhookStatus })
  @IsEnum(WebhookStatus)
  @IsNotEmpty()
  status: WebhookStatus;

  @ApiProperty({ example: ["0xabc123...","0xdef456..."] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  wallet_addresses?: string[] = []; 

}