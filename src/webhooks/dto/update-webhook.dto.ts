
import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsEnum, IsNotEmpty, IsObject, IsOptional, IsString, ValidateNested } from "class-validator";
import { WebhookStatus } from "../webhooks.status.enum";

class DestinationAttributesDto {
  @ApiProperty({ example: "https://scanner.cake5x.com/transaction-router/receive/1" })
  @IsOptional()
  @IsString()
  url?: string;
}

class MonitorAddressesDto {
  @ApiProperty({ example: ["0xabc123...", "0xdef456..."] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  wallets?: string[];
}

export class UpdateWebhookDto {
  @ApiProperty({ example: "bananaqwerama"})
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ example: { url: "https://scanner.cake5x.com/transaction-router/receive/1" }})
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => DestinationAttributesDto)
  destination_attributes?: DestinationAttributesDto;

  @ApiProperty({ example: WebhookStatus.ACTIVE, enum: WebhookStatus})
  @IsOptional()
  @IsEnum(WebhookStatus)
  status?: WebhookStatus;

  @ApiProperty({ example: { wallets: ["0xabc123...", "0xdef456..."] } })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => MonitorAddressesDto)
  monitorAddresses?: MonitorAddressesDto;
}