import { Module } from '@nestjs/common';
import { WebhooksService } from './webhooks.service';
import { WebhooksController } from './webhooks.controller';
import { NetworksModule } from 'src/networks/networks.module';
import { ApiKeyModule } from 'src/api-key/api-key.module';
import { WebhookDeliveryService } from './webhook-delivery.service';
import { QuicknodeModule } from 'src/quicknode/quicknode.module';

@Module({
  controllers: [WebhooksController],
  providers: [WebhooksService, WebhookDeliveryService],
  imports: [NetworksModule, ApiKeyModule, QuicknodeModule],
  exports: [WebhookDeliveryService]
})
export class WebhooksModule {}
