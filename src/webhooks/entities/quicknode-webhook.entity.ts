import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, DeleteDateColumn, Index } from 'typeorm';

export enum QuicknodeWebhookStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  TERMINATED = 'terminated',
}

@Entity('quicknode_webhooks')
@Index(['network_id'])
@Index(['qn_webhook_id'])
export class QuicknodeWebhook {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  network_id: number;

  @Column({ type: 'uuid' })
  qn_webhook_id: string; 

  @Column({ type: 'varchar' })
  webhook_name: string; 

  @Column({ type: 'varchar' })
  destination_url: string; 

  @Column({ type: 'varchar' })
  security_token: string; 

  @Column({ type: 'enum', enum: QuicknodeWebhookStatus, default: QuicknodeWebhookStatus.ACTIVE })
  status: QuicknodeWebhookStatus;

  @Column({ type: 'int', default: 0 })
  address_count: number; 

  @Column({ type: 'int', default: 1000 })
  max_address_capacity: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn({ nullable: true })
  deleted_at: Date;
}