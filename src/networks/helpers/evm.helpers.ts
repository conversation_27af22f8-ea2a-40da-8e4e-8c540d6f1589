import { HttpService } from '@nestjs/axios';
import { BadRequestException, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Network, NetworkType } from '../entities/network.entity';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import configuration from 'config/configuration';
import { firstValueFrom } from 'rxjs';
import { SupportedNetworks } from './helpers.enum';

@Injectable()
export class EVMHelper  {
  private readonly supportedType = NetworkType.EVM;
  private readonly quicknodeEndpoint: string;
  private readonly quicknodeToken: string;

  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    private readonly httpService: HttpService
  ) {
    this.quicknodeEndpoint = configuration().qn_endpoint;
    this.quicknodeToken = configuration().qn_endpoint_token;

    if (!this.quicknodeEndpoint || !this.quicknodeToken) {
      throw new Error('QuickNode credentials not configured in environment');
    }
  }

  async getBlockNumber(network_name: string, networkType: NetworkType): Promise<any> {
    try {
      // Check if this network exists( delete )
      if (networkType !== this.supportedType) {
        throw new BadRequestException(`EVMHelper only supports ${this.supportedType} networks`);
      }

      // Route to appropriate network handler and return raw response
      const rawResponse = await this.routeToNetwork(network_name);
      return rawResponse;
    } catch (error) {
      throw new BadRequestException(`EVM helper failed for ${network_name}: ${error.message}`);
    }
  }

  private async routeToNetwork(network_name: string): Promise<any> {
    const normalizedName = network_name.toLowerCase();

    switch (normalizedName) {
      case SupportedNetworks.ETHEREUM_MAINNET:
        return await this.ethereumMainnet();
      
      case SupportedNetworks.ETHEREUM_SEPOLIA:
        return await this.ethereumSepolia();

      case SupportedNetworks.BSC_MAINNET:
        return await this.bscMainnet();

      case SupportedNetworks.BSC_SEPOLIA:
        return await this.bscSepolia();
      
      case SupportedNetworks.POLYGON_MAINNET:
        return await this.polygonMainnet();

      case SupportedNetworks.POLYGON_SEPOLIA:
        return await this.polygonSepolia();

      case SupportedNetworks.BASE_MAINNET:
        return await this.baseMainnet();

      case SupportedNetworks.BASE_SEPOLIA:
        return await this.baseSepolia();

      case SupportedNetworks.ARBITRUM_MAINNET:
        return await this.arbitriumMainnet();

      case SupportedNetworks.ARBITRUM_SEPOLIA:
        return await this.arbitriumSepolia();
      
      default:
        throw new BadRequestException(`Unsupported EVM network: ${network_name}`);
    }
  }


  // ethereum-mainnet
  private async ethereumMainnet(): Promise<any> {
    const networkConfig = await this.getNetworkConfig(SupportedNetworks.ETHEREUM_MAINNET);
    const url = this.buildQuickNodeURL(SupportedNetworks.ETHEREUM_MAINNET);
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Ethereum Sepolia handler
  private async ethereumSepolia(): Promise<any> {
    const networkConfig = await this.getNetworkConfig(SupportedNetworks.ETHEREUM_SEPOLIA);
    const url = this.buildQuickNodeURL(SupportedNetworks.ETHEREUM_SEPOLIA);
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Binance Smart Chain bsc-mainnet
  private async bscMainnet(): Promise<any> {
    const networkConfig = await this.getNetworkConfig(SupportedNetworks.BSC_MAINNET);
    const url = this.buildQuickNodeURL(SupportedNetworks.BSC_MAINNET);
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Binance Smart Chain bsc-sepolia 
  private async bscSepolia(): Promise<any> {
    const networkConfig = await this.getNetworkConfig(SupportedNetworks.BSC_SEPOLIA);
    const url = this.buildQuickNodeURL(SupportedNetworks.BSC_SEPOLIA);
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Polygon mainnet 
  private async polygonMainnet(): Promise<any> {
    const networkConfig = await this.getNetworkConfig(SupportedNetworks.POLYGON_MAINNET);
    const url = this.buildQuickNodeURL(SupportedNetworks.POLYGON_MAINNET);
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Polygon Sepolia
  private async polygonSepolia(): Promise<any> {
    const networkConfig = await this.getNetworkConfig(SupportedNetworks.POLYGON_SEPOLIA);
    const url = this.buildQuickNodeURL(SupportedNetworks.POLYGON_SEPOLIA);
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Base mainnet
  private async baseMainnet(): Promise<any> {
    const networkConfig = await this.getNetworkConfig(SupportedNetworks.BASE_MAINNET);
    const url = this.buildQuickNodeURL(SupportedNetworks.BASE_MAINNET);
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Base Sepolia 
  private async baseSepolia(): Promise<any> {
    const networkConfig = await this.getNetworkConfig(SupportedNetworks.BASE_SEPOLIA);
    const url = this.buildQuickNodeURL(SupportedNetworks.BASE_SEPOLIA);
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Arbitrium mainnet 
  private async arbitriumMainnet(): Promise<any> {
    const networkConfig = await this.getNetworkConfig(SupportedNetworks.ARBITRUM_MAINNET);
    const url = this.buildQuickNodeURL(SupportedNetworks.ARBITRUM_MAINNET);
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  // Arbitrium Sepolia 
  private async arbitriumSepolia(): Promise<any> {
    const networkConfig = await this.getNetworkConfig(SupportedNetworks.ARBITRUM_SEPOLIA);
    const url = this.buildQuickNodeURL(SupportedNetworks.ARBITRUM_SEPOLIA);
    return await this.makeRPCCall(url, networkConfig.rpc_payload);
  }

  private async getNetworkConfig(network_name: string): Promise<any> {
  const network = await this.entityManager
    .createQueryBuilder(Network, 'network')  
    .where('network.display_name = :network_name AND network.is_active = true', { network_name })
    .getOne();

  if (!network) {
    throw new BadRequestException(`Network configuration not found: ${network_name}`);
  }

  return {
    display_name: network.display_name,
    rpc_payload: network.rpc_payload,
    type: network.type,
    is_active: network.is_active,
  };
}


  // QuickNode URL builder
  private buildQuickNodeURL(network_name: string): string {
    // ETH mainnet is differently abled ( special is more ways than naught)
    if (network_name === SupportedNetworks.ETHEREUM_MAINNET) {
      return `https://${this.quicknodeEndpoint}.quiknode.pro/${this.quicknodeToken}/`;
    }
    // Standard URL structure for all other networks
    return `https://${this.quicknodeEndpoint}.${network_name}.quiknode.pro/${this.quicknodeToken}/`;
  }

  private async makeRPCCall(url: string, payload: any): Promise<any> {
    const payloadStringy = JSON.stringify(payload);
    try {
      const response = await firstValueFrom(
        this.httpService.post(url, payloadStringy, {
          headers: { 'Content-Type': 'application/json' },
        })
      );
      return response.data.result;
    } catch (error: any) {
      // Fallback if cert mismatch is the problem [ CERT ERROR ]
      if (error.message?.includes("Hostname/IP does not match")) {
        const fallbackUrl = url.replace(/\.[^.]+\.quiknode\.pro/, ".quiknode.pro");
        const response = await firstValueFrom(
          this.httpService.post(fallbackUrl, payloadStringy, {
            headers: { 'Content-Type': 'application/json' },
          })
        );
        return response.data.result;
      }

      throw new HttpException(
        error.message || "RPC call failed",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }


  public getSupportedNetworks(): string[] {
    return [
      SupportedNetworks.ETHEREUM_MAINNET,
      SupportedNetworks.ETHEREUM_SEPOLIA, 
      SupportedNetworks.BSC_MAINNET,
      SupportedNetworks.BSC_SEPOLIA,
      SupportedNetworks.POLYGON_MAINNET,
      SupportedNetworks.POLYGON_SEPOLIA,
      SupportedNetworks.BASE_MAINNET,
      SupportedNetworks.BASE_SEPOLIA,
      SupportedNetworks.ARBITRUM_MAINNET,
      SupportedNetworks.ARBITRUM_SEPOLIA,
    ];
  }
}