export enum SupportedNetworks {
  ETHEREUM_MAINNET = 'ethereum-mainnet',
  ETHEREUM_SEPOLIA = 'ethereum-sepolia',

  BSC_MAINNET = 'bsc',
  BSC_SEPOLIA = 'bsc-sepolia',

  POLYGON_MAINNET = 'matic',
  POLYGON_SEPOLIA = 'matic-sepolia',

  BASE_MAINNET = 'base-mainnet',
  BASE_SEPOLIA = 'base-sepolia',

  ARBITRUM_MAINNET = 'arbitrum-mainnet',
  ARBITRUM_SEPOLIA = 'arbitrum-sepolia',

  SOLANA_MAINNET = 'solana-mainnet',
  SOLANA_DEVNET = 'solana-devnet',
}
