import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EVMHelper } from './evm.helpers';
import { Network } from '../entities/network.entity';
import { HttpModule } from '@nestjs/axios';
import { SolanaHelper } from './solana.helpers';

@Module({
  imports: [
    HttpModule,
    ConfigModule,
    TypeOrmModule.forFeature([Network])
  ],
  providers: [EVMHelper, SolanaHelper],
  exports: [EVMHelper , SolanaHelper],
})
export class HelpersModule {}
