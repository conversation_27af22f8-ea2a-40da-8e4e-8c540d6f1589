import { Type } from 'class-transformer';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

export enum NetworkType {
  EVM = 'EVM',
  SOLANA = 'SOLANA', // For sealevel chains [ thats what solanan calls them programs not contracts like EVM]
   UTXO = 'UTXO', 
  SUBSTRATE = 'SUBSTRATE', // For Polkadot/Kusama
  COSMOS = 'COSMOS', // For Cosmos SDK chains
  TRON = 'TRON' // For Tron
}


@Entity({ name: 'networks' })
export class Network {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  display_name: string; // ethereum-mainnet

  @Column()
  name: string; // Ethereum Mainnet

  @Column()
  symbol: string; // ETH

  @Column({ unique: true , nullable: true })
  chain_id: number; // 1

  @Column({ type: 'enum', enum: NetworkType })
  type: NetworkType; // EVM , BTC , SOLANA , SUBSTRATE

  @Column()
  block_time_seconds: number; // 1-12

  @Column({ type: 'json' })
  rpc_payload: object; // Store the JSON-RPC body

  @Column({ default: true })
  is_active: boolean;

  @Column({ default: false })
  is_testnet: boolean;

  @CreateDateColumn()
  created_at: Date;
}