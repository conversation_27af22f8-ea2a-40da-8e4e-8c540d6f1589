import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Logger } from '@nestjs/common';
import { TransactionRouterService } from './transaction-router.service';

@Controller('transaction-router')
export class TransactionRouterController {
  private readonly logger = new Logger(TransactionRouterController.name);

  // testing steps 
  // create 1 webhook.siteURL n one typedwebhookURL  
  // create 2 webhook 
  // getALL and verify webhook creation 
  // run the test function 

  constructor(
    private readonly transactionRouterService: TransactionRouterService
  ) {}

  // Quicknode sends transaction data to this endpoint
  @Post('receive/:network_id')
  async receiveTransactionData(@Param('network_id') networkId: string, @Body() payload: any) {
    const startTime = Date.now();
    try {
      this.logger.log(`Network ${networkId}: Processing incoming data`);

      // Send the entire payload
      const result = await this.transactionRouterService.processIncomingData(parseInt(networkId), payload);
      const processingTime = Date.now() - startTime;

      if (result.success) {
        const successResult = result as any; // Type assertion for successful result
        this.logger.log(`🤑 Completed processing: ${successResult.transactions?.saved || 0} transactions, ${successResult.webhooks?.delivered || 0} webhooks delivered`);
      } else {
        this.logger.error(`Processing failed: ${result.error}`);
      }

      return {
        status: 'success',
        message: 'Data processed successfully',
        processingTime
      };
    }catch (error) {
      const processingTime = Date.now() - startTime;
      
      this.logger.error( `💥 BOOOMMMM!!! Controller error for network ${networkId}: ${error.message}`, error.stack);

      return {
        status: 'error',
        message: 'Internal processing error',
        processingTime
      };
    }
  }

  // Fake quicknode data for testing - now with multiple transactions
  @Post('test/:network_id')
  async testWithRealData(@Param('network_id') networkId: string) {
    const realQuickNodePayload = {
      data: [{
        block: {
          baseFeePerGas: "0xa14aa46",
          gasLimit: "0x2aea540", 
          gasUsed: "0x12d177a",
          hash: "0xb16d7b1ae34ed3f75d9fbd85a2c1f51bb8d6efe2f721f8e9a3eda6c7937df5fd",
          number: "0x16228ce",
          timestamp: "0x68aae1af",
          transactions: [
            {
              blockHash: "0xb16d7b1ae34ed3f75d9fbd85a2c1f51bb8d6efe2f721f8e9a3eda6c7937df5fd",
              blockNumber: "0x16228ce",
              from: "0x31384E21D3df6F69DB15859DBE0e130ceab2398e", // Test address 1
              gas: "0x6be17",
              gasPrice: "0xa14aa46",
              maxFeePerGas: "0x1596a8be",
              maxPriorityFeePerGas: "0x0",
              hash: "0xf970471fba5c0a2f95c33f1721cb3e45616c4c32dadddde22f460c43efc4126e",
              input: "0x09c5eabe000000",
              nonce: "0x4be5",
              to: "0xEd7923a4F881F26C3d76ed17D28cc44dDa5Cd4C8", // Test address 2  
              transactionIndex: "0x0",
              value: "0x58d15e17628000",
              type: "0x2",
              chainId: "0x1"
            },
            {
              blockHash: "0xb16d7b1ae34ed3f75d9fbd85a2c1f51bb8d6efe2f721f8e9a3eda6c7937df5fd",
              blockNumber: "0x16228ce", 
              from: "0xEd7923a4F881F26C3d76ed17D28cc44dDa5Cd4C8", // Test address 2
              gas: "0x1dd3b",
              gasPrice: "0xa14aa46",
              maxFeePerGas: "0xa14aa46",
              maxPriorityFeePerGas: "0x2",
              hash: "0xf36021499c612095097560cb95d059abf1bac30894d78818f92d4e557c2abadf",
              input: "0x7c3ab2617246d0c6c0087f18703d576831899ca94f01",
              nonce: "0x4d0991",
              to: "0x742d35cc6097db19b6c2e4a6d3f3b8b1a1d2d8b1", // Test address 3
              transactionIndex: "0x1", 
              value: "0x20e6839af",
              type: "0x2",
              chainId: "0x1"
            },
            {
              blockHash: "0xb16d7b1ae34ed3f75d9fbd85a2c1f51bb8d6efe2f721f8e9a3eda6c7937df5fd",
              blockNumber: "0x16228ce",
              from: "0x999888777666555444333222111000aaabbbcccd", // Unmonitored
              gas: "0x5208",
              gasPrice: "0xa14aa46", 
              hash: "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
              nonce: "0x100",
              to: "0x555666777888999aaabbbcccdddeeefffaabbcc", // Unmonitored
              transactionIndex: "0x2",
              value: "0x6f05b59d3b20000",
              type: "0x2",
              chainId: "0x1"
            }
          ]
        }
      }]
    };

    this.logger.log(`Testing with real QuickNode format: ${realQuickNodePayload.data[0].block.transactions.length} transactions`);
    return await this.receiveTransactionData(networkId, realQuickNodePayload);
  }







}
