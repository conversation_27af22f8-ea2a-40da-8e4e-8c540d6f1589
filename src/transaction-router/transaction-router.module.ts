import { Module } from '@nestjs/common';
import { TransactionRouterService } from './transaction-router.service';
import { TransactionRouterController } from './transaction-router.controller';
import { WebhooksModule } from '../webhooks/webhooks.module';

@Module({
  controllers: [TransactionRouterController],
  providers: [TransactionRouterService],
  imports: [WebhooksModule],
})
export class TransactionRouterModule {}
