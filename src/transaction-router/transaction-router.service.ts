import { Injectable, Logger } from '@nestjs/common';
import { CreateTransactionRouterDto } from './dto/create-transaction-router.dto';
import { UpdateTransactionRouterDto } from './dto/update-transaction-router.dto';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { Transaction, TransactionStatus } from './entities/transactions.entity';
import { WebhookDeliveryService } from 'src/webhooks/webhook-delivery.service';
import { Network } from 'src/networks/entities/network.entity';
import { RawTransactions } from './entities/raw-transaction.entity';
import { Hash } from 'node:crypto';

@Injectable()
export class TransactionRouterService {
  private readonly logger = new Logger(TransactionRouterService.name);

  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    private webhookDeliveryService: WebhookDeliveryService
  ) {}

  // ==================== TRANSACTION METHODS ====================
  private formatIncomingData(rawData: any): { transactions: any[], blockData?: any } {
    // Handle block-based structure (regular webhooks)
    if (rawData?.data && Array.isArray(rawData.data)) {
      const allTransactions = [];
      let latestBlockData = null;

      for (const blockData of rawData.data) {
        if (blockData?.block?.transactions) {
          const transactionsWithBlock = blockData.block.transactions.map(tx => ({
            ...tx,
            blockNumber: tx.blockNumber || blockData.block.number,
            blockTimestamp: blockData.block.timestamp
          }));
          allTransactions.push(...transactionsWithBlock);
          latestBlockData = blockData.block; // Keep latest block info
        }
      }
      
      return { transactions: allTransactions, blockData: latestBlockData };
    }

    // Handle matched transactions structure (filtered webhooks)
    if (rawData?.matchingTransactions && Array.isArray(rawData.matchingTransactions)) {
      return { transactions: rawData.matchingTransactions };
    }

    return { transactions: [] };
  }

  private mapTransactionStatus(status: string): TransactionStatus {
    switch (status) {
      case '0x1':
        return TransactionStatus.SUCCESS;
      case '0x0':
        return TransactionStatus.FAILED;
      default:
        return TransactionStatus.PENDING; // Default for unknown status
    }
  }

  async saveTransactions(networkId: number, transactionData: any[]) {
    // block data optional as matched transactions dont have block data
    if (!transactionData || transactionData.length === 0) {
      return { 
        success: true, 
        transactions: { saved: 0, total: 0, processed: [] },
        rawTransactions: { saved: 0, total: 0 }
      };
    }

    // etheruem uses blocks solana doesnt use blocks  so the structure is diffrent handle later forcus on this for now 

    // Debug: Log the structure of incoming data
    this.logger.debug('Saved Booiii data structure:', JSON.stringify(transactionData, null, 2));

    try {

      // prep format transactions db 
      const dbTransactions = transactionData.map((tx: any) => ({
        hash: tx.hash,
        networkId: networkId,
        from: tx.from ? tx.from.toLowerCase() : null,
        to: tx.to ? tx.to.toLowerCase() : null,
        value: tx.value,
        blockNumber: tx.blockNumber ,
        blockTimestamp: tx.blockTimestamp,
        transactionIndex: tx.transactionIndex,
        nonce: tx.nonce,
        gasPrice: tx.gasPrice || tx.maxFeePerGas,
        gasUsed: tx.gas || null,
        chainId: tx.chainId || `0x${networkId}`,
        transactionType: tx.type,
        status: this.mapTransactionStatus(tx.status)
      }));

      // Prepare raw transactions for database
      const rawTransactions = transactionData.map((tx: any) => ({
        hash: tx.hash,
        blockNumber: tx.blockNumber,
        rawJson: tx // Store the entire merged object
      }));

      await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(Transaction)
        .values(dbTransactions)
        .orIgnore()
        .execute();

      await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(RawTransactions)
        .values(rawTransactions)
        .execute();

      // Format transactions for webhook delivery
      const webhookTransactions = transactionData.map((tx: any) => ({
        hash: tx.hash,
        from: tx.from?.toLowerCase() || null,
        to: tx.to?.toLowerCase() || null,
        value: tx.value,
        blockNumber: tx.blockNumber,
        blockTimestamp: tx.blockTimestamp,
        gasPrice: tx.gasPrice,
        transactionIndex: tx.transactionIndex,
        nonce: tx.nonce
      }));

      // Save to database
      this.logger.log(`Processed ${transactionData.length} transactions, saved ${dbTransactions.length} new ones`);

      return {
        success: true,
        transactions: {
          saved: dbTransactions.length,
          total: transactionData.length,
          processed: webhookTransactions
        },
        rawTransactions: {
          saved: rawTransactions.length,
          total: rawTransactions.length
        }
      };

      } catch (error) {
        if (error.message.includes('duplicate key')) {
          this.logger.debug(`Skipped ${transactionData.length} duplicate transactions`);
          
          // Still return webhook transactions even if duplicates
          const webhookTransactions = transactionData.map((tx: any) => ({
            hash: tx.hash,
            from: tx.from?.toLowerCase() || null,
            to: tx.to?.toLowerCase() || null,
            value: tx.value,
            blockNumber: tx.blockNumber ,
            blockTimestamp: tx.blockTimestamp ,
            gasPrice: tx.gasPrice,
            transactionIndex: tx.transactionIndex,
            nonce: tx.nonce
          }));

          return {
            success: true,
            transactions: {
              saved: 0,
              total: transactionData.length,
              processed: webhookTransactions
            },
            rawTransactions: {
              saved: 0,
              total: transactionData.length
            }
          };
        }
        
        this.logger.error(`Transaction saving failed: ${error.message}`);
        return {
          success: false,
          error: error.message,
          transactions: { saved: 0, total: transactionData.length, processed: [] },
          rawTransactions: { saved: 0, total: transactionData.length }
        };
      }
  }


  async getNetwork(networkId: number): Promise<Network> {
    try {
      return await this.entityManager
        .createQueryBuilder(Network, 'network')
        .where('network.id = :networkId', { networkId })
        .getOne();
    } catch (error) {
      this.logger.error(`Failed to get network ${networkId}: ${error.message}`);
      return null;
    }
  }

  // connected to the webhookDeliveryService to deliver the webhooks 
  private async deliverWebhooks(networkId: number, transactions: any[], networkName: string) {
    try {
      this.logger.log(`WE deliverin ${transactions.length} transactions on ${networkName}`);
      
      const result = await this.webhookDeliveryService.routeTransactionsToWebhooks(
        networkId,
        transactions,
        networkName
      );

      return {
        found: result.webhooksFound,
        delivered: result.webhooksDelivered,
        failed: result.webhooksFailed,
        message: result.message
      };

    } catch (error) {
      this.logger.error(`Webhook delivery error: ${error.message}`);
      return {
        found: 0,
        delivered: 0,
        failed: 0,
        message: `Webhook delivery failed: ${error.message}`
      };
    }
  }

  // ==================== MAIN PROCESS ====================
  async processIncomingData(networkId: number, rawData: any) {
    this.logger.debug('Raw data structure bolck_with_receipts:', JSON.stringify(rawData, null, 2));

    // Step 1: Validate input
    if ( !rawData?.TransactionData || !Array.isArray(rawData.TransactionData) || rawData.TransactionData.length === 0) {
      return {
        success: false,
        error: 'No transactions found in payload'
      };
    }

    // Step 2: Extract and save transactions
    const transactionResult = await this.saveTransactions(networkId, rawData.TransactionData);
    if(!transactionResult.success){
      return {
        success: false,
        error: `Transaction saving failed: ${transactionResult.error}`,
        transactions: { saved: 0, total: 0 }
      };
    }

    // Step 3: Get network info for webhook delievery 
    const network = await this.getNetwork(networkId);
    if (!network) {
      return {
        success: false,
        error: `Network ${networkId} not found`,
        transactions: transactionResult
      };
    }

    // Step 4: Deliver webhooks (if we have transactions)
    let webhookResult = { delivered: 0, failed: 0, found: 0, message: 'There are no ships in the sea Today' };
    if (transactionResult.transactions.processed.length > 0) {
      webhookResult = await this.deliverWebhooks(networkId, transactionResult.transactions.processed, network.display_name);
    }
    return {
      success: true,
      transactions: {
        saved: transactionResult.transactions.saved,
        total: transactionResult.transactions.total
      },
      rawTransactions: {
        saved: transactionResult.rawTransactions.saved,
        total: transactionResult.rawTransactions.total
      },
      webhooks: {
        found: webhookResult.found,
        delivered: webhookResult.delivered,
        failed: webhookResult.failed,
        message: webhookResult.message
      }
    };
  }

  async getRecentRawTransactions(limit: number = 10) {
    try {
      return await this.entityManager
        .createQueryBuilder(RawTransactions, 'raw')
        .orderBy('raw.createdAt', 'DESC')
        .limit(limit)
        .getMany();
    } catch (error) {
      this.logger.error(`Failed to get recent raw transactions: ${error.message}`);
      return [];
    }
  }





  
}
